import { BUSINESS_TYPE_CHECKS } from "../constants/businessConstants";
import { isApprovedMCCCode } from "../constants/mccCodes";

export interface ValidationErrors {
  [key: string]: string;
}

export interface FormData {
  name?: string;
  type?: number;
  ein?: string;
  website?: string;
  email?: string;
  phone?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  public?: number;
  merchant?: {
    dba?: string;
    new?: number;
    mcc?: string;
    status?: string;
    established?: string;
    avgTicket?: number;
    annualCCSales?: number;
    members?: Array<{
      title?: string;
      first?: string;
      middle?: string;
      last?: string;
      ssn?: string;
      dob?: string;
      dl?: string;
      dlstate?: string;
      ownership?: number;
      significantResponsibility?: number;
      politicallyExposed?: number;
      email?: string;
      phone?: string;
      primary?: string;
      address1?: string;
      address2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    }>;
  };
}

export const validateBusinessInfoForm = (formData: FormData): ValidationErrors => {
  const errors: ValidationErrors = {};
  const { requiresCorporateStructure, isSoleProprietor } = BUSINESS_TYPE_CHECKS;

  if (!formData.name?.trim()) errors.name = "Legal business name is required";
  if (!formData.type) errors.type = "Business type is required";
  if (!formData.ein?.trim()) {
    errors.ein = formData.type && isSoleProprietor(formData.type) ? "Tax ID/EIN or SSN is required" : "Tax ID/EIN is required";
  }
  if (!formData.website?.trim()) errors.website = "Website is required";
  if (!formData.email?.trim()) errors.email = "Business email is required";
  if (!formData.phone?.trim()) errors.phone = "Business phone is required";
  if (!formData.address1?.trim()) {
    errors.address1 = "Address is required";
  } else if (/^(p\.?\s?o\.?\s?box|post\s?office\s?box)/i.test(formData.address1)) {
    errors.address1 = "PO Boxes are not acceptable for business address";
  }
  if (!formData.city?.trim()) errors.city = "City is required";
  if (!formData.state?.trim()) errors.state = "State is required";
  if (!formData.zip?.trim()) errors.zip = "ZIP code is required";
  if (!formData.merchant?.mcc?.trim()) errors.mcc = "MCC code is required";
  if (!formData.merchant?.established?.trim()) errors.established = "Business establishment date is required";

  // Note: annualCCSales validation removed as it's automatically set based on merchant status

  if (formData.type && requiresCorporateStructure(formData.type) && !formData.merchant?.dba?.trim()) {
    errors.dba = "DBA/Statement descriptor is required for this business type";
  }

  if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = "Please enter a valid email address";
  }

  if (formData.phone && !/^\d{10}$/.test(formData.phone.replace(/\D/g, ""))) {
    errors.phone = "Phone must be 10 digits";
  }

  if (formData.ein && !/^\d{9}$/.test(formData.ein.replace(/\D/g, ""))) {
    errors.ein = "EIN must be 9 digits";
  }

  if (formData.zip && !/^\d{5}(-\d{4})?$/.test(formData.zip)) {
    errors.zip = "ZIP code must be 5 digits or 5-4 format";
  }

  if (formData.merchant?.mcc) {
    if (!/^\d{4}$/.test(formData.merchant.mcc)) {
      errors.mcc = "MCC must be 4 digits";
    } else if (!isApprovedMCCCode(formData.merchant.mcc)) {
      errors.mcc = "This MCC code is not approved for merchant registration. Please select from the available options.";
    }
  }

  return errors;
};

export const validateOwnershipStructure = (formData: FormData): ValidationErrors => {
  const errors: ValidationErrors = {};
  const members = formData.merchant?.members || [];
  const entityType = formData.type;

  if (members.length === 0) {
    errors.members = "At least one business owner is required";
    return errors;
  }

  // Check for multiple primary members
  const primaryMembers = members.filter((member) => member.primary === "1");
  if (primaryMembers.length > 1) {
    errors.primary = "Only one member can be marked as primary";
  }

  // Validate ownership percentages
  const totalOwnership = members.reduce((sum, member) => sum + (member.ownership || 0), 0);
  if (totalOwnership > 10000) {
    errors.ownership = "Total ownership cannot exceed 100% (10000 basis points)";
  }

  // Entity type specific validations
  if (entityType === 1) {
    // Sole Proprietorship
    if (members.length !== 1) {
      errors.soleProprietor = "Sole proprietorship must have exactly one owner";
    } else if (members[0].ownership !== 10000) {
      errors.soleProprietorOwnership = "Sole proprietorship owner must have 100% ownership (10000 basis points)";
    }
  }

  if (entityType === 5 || entityType === 6) {
    // Government or Non-profit
    const hasOwnership = members.some((member) => member.ownership && member.ownership > 0);
    if (hasOwnership) {
      errors.governmentNonProfit = "Government and non-profit entities should not have ownership structure";
    }
  }

  return errors;
};

export const validateMerchantBusinessRules = (formData: FormData): ValidationErrors => {
  const errors: ValidationErrors = {};

  // Validate that merchant status and annualCCSales are consistent
  if (formData.merchant?.status === "1" && formData.merchant?.annualCCSales !== 0) {
    errors.annualCCSalesStatus = "When merchant status is '1', annual CC sales should be 0";
  }

  // Validate new merchant flag
  if (formData.merchant?.new !== undefined && formData.merchant.new !== 0 && formData.merchant.new !== 1) {
    errors.newMerchant = "New merchant flag must be 0 or 1";
  }

  // Ensure public field is 0 for merchant entities
  if (formData.public !== 0) {
    errors.publicEntity = "Merchant entities must be private companies (public = 0)";
  }

  return errors;
};

export interface BankAccountData {
  account: {
    routing?: string;
    number?: string;
    method?: number;
  };
}

export interface BankAccountValidationErrors {
  routing?: string;
  number?: string;
  terms?: string;
  voidCheck?: string;
}

export const validateBankAccount = (
  account: BankAccountData,
  termsAccepted: boolean,
  verificationMethod: "manual" | "upload",
  uploadedFile: File | null
): BankAccountValidationErrors => {
  const errors: BankAccountValidationErrors = {};

  if (!account.account.routing?.trim()) {
    errors.routing = "Routing number is required";
  } else if (!/^\d{9}$/.test(account.account.routing.replace(/\D/g, ""))) {
    errors.routing = "Routing number must be 9 digits";
  }

  if (!account.account.number?.trim()) {
    errors.number = "Account number is required";
  } else if (!/^\d{4,17}$/.test(account.account.number.replace(/\D/g, ""))) {
    errors.number = "Account number must be between 4-17 digits";
  }

  if (!termsAccepted) {
    errors.terms = "You must acknowledge and agree to the terms and conditions";
  }

  if (verificationMethod === "upload" && !uploadedFile) {
    errors.voidCheck = "Please upload a void check image";
  }

  return errors;
};
