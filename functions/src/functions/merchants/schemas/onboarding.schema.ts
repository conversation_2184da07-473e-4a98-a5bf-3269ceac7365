import { z } from "zod";
import { isApprovedMCCCode, getMCCCodeDetails } from "../../../constants/approvedMccCodes.js";

// Helper schemas for reusable validation patterns
const phoneSchema = z
  .string()
  .min(10, "Phone number must be at least 10 digits")
  .regex(/^\+?[\d\s\-()]+$/, "Invalid phone number format")
  .transform((val) => val.replace(/\D/g, "")); // Strip non-digits

const emailSchema = z.string().email("Invalid email format").min(1, "Email is required");

const einSchema = z
  .string()
  .min(1, "EIN is required")
  .regex(/^\d{2}-?\d{7}$/, "EIN must be 9 digits (XX-XXXXXXX or XXXXXXXXX format)")
  .transform((val) => val.replace(/\D/g, "")); // Strip non-digits

const zipSchema = z
  .string()
  .min(5, "Zip code must be at least 5 digits")
  .max(10, "Zip code too long")
  .regex(/^\d{5}(-?\d{4})?$/, "Invalid zip code format");

const routingSchema = z
  .string()
  .length(9, "Routing number must be exactly 9 digits")
  .regex(/^\d{9}$/, "Routing number must contain only digits");

const mccSchema = z
  .string()
  .length(4, "MCC code must be exactly 4 digits")
  .regex(/^\d{4}$/, "MCC code must contain only digits")
  .refine(
    (mccCode) => isApprovedMCCCode(mccCode),
    (mccCode) => {
      const details = getMCCCodeDetails(mccCode);
      return {
        message: details
          ? `MCC code ${mccCode} (${details.description}) is not approved for merchant registration. Please contact support for approved business categories.`
          : `MCC code ${mccCode} is not approved for merchant registration. Only specific business categories are currently supported.`,
      };
    }
  );

const dobSchema = z
  .string()
  .min(1, "Date of birth is required")
  .regex(/^\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])$/, "Date of birth must be in YYYYMMDD format");

const tcDateSchema = z
  .string()
  .min(1, "Terms & Conditions acceptance date is required")
  .regex(/^\d{12}$/, "TC date must be in YYYYMMDDHHMM format");

// Account schema
const accountSchema = z.object({
  primary: z.number().int().min(0).max(1, "Primary must be 0 or 1"),
  currency: z.string().default("USD"),
  account: z.object({
    method: z
      .number()
      .int()
      .refine(
        (val) => [8, 9, 10, 11].includes(val),
        "Account method must be 8 (personal checking), 9 (personal savings), 10 (business checking), or 11 (business savings)"
      ),
    number: z.string().min(1, "Account number is required"),
    routing: routingSchema,
  }),
});

// Member schema
const memberSchema = z.object({
  first: z.string().min(1, "First name is required"),
  last: z.string().min(1, "Last name is required"),
  email: emailSchema,
  dob: dobSchema,
  title: z.string().min(1, "Business title is required"),
  phone: phoneSchema,
  address1: z.string().min(1, "Address is required"),
  address2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  state: z.string().min(2, "State is required").max(2, "State must be 2 characters"),
  zip: zipSchema,
  country: z.string().default("USA"),
  ssn: z.string().optional(),
  ownership: z
    .number()
    .int()
    .min(1, "Ownership percentage must be at least 1%")
    .max(10000, "Ownership percentage cannot exceed 100% (10000 basis points)"),
  significantResponsibility: z.number().int().min(0).max(1, "Significant responsibility must be 0 or 1"),
  politicallyExposed: z.number().int().min(0).max(1, "Politically exposed must be 0 or 1"),
  primary: z.string().optional(),
});

// Merchant nested object schema
const merchantSchema = z.object({
  dba: z.string().min(1, "DBA name is required"),
  mcc: mccSchema,
  status: z.string().min(1, "Merchant status is required"),
  new: z.number().int().min(0).max(1, "New merchant flag must be 0 or 1").default(1),
  annualCCSales: z.number().int().min(0, "Annual CC sales must be a positive number").optional().default(0),
  avgTicket: z.number().int().min(0, "Average ticket must be a positive number").optional().default(50),
  established: z.string().optional(),
  members: z
    .array(memberSchema)
    .min(1, "At least one business owner is required")
    .refine(
      (members) => members.reduce((sum, member) => sum + member.ownership, 0) <= 10000,
      "Total ownership cannot exceed 100% (10000 basis points)"
    )
    .refine((members) => members.filter((member) => member.primary === "1").length <= 1, "Only one member can be marked as primary"),
});

// Main onboarding request schema
export const OnboardingRequestSchema = z
  .object({
    // Top-level entity fields
    name: z.string().min(1, "Legal business name is required"),
    address1: z.string().min(1, "Address is required"),
    address2: z.string().optional(),
    city: z.string().min(1, "City is required"),
    state: z.string().min(2, "State is required").max(2, "State must be 2 characters"),
    zip: zipSchema,
    country: z.string().default("USA"),
    phone: phoneSchema,
    email: emailSchema,
    ein: einSchema,
    website: z.string().min(1, "Website is required").url("Website must be a valid URL").or(z.literal("https://nowebsite.com")), // Allow default placeholder
    type: z.number().int().min(1, "Business type is required"),
    public: z.number().int().min(0).max(1, "Public designation must be 0 or 1").default(0),
    currency: z.string().default("USD"),

    // Payrix-specific fields
    tcVersion: z.string().min(1, "Terms & Conditions version is required"),
    tcDate: tcDateSchema,
    clientIp: z.string().min(1, "Client IP address is required").ip("Invalid IP address format"),

    // Nested objects
    merchant: merchantSchema,
    accounts: z
      .array(accountSchema)
      .min(1, "At least one bank account is required")
      .refine((accounts) => accounts.filter((acc) => acc.primary === 1).length === 1, "Exactly one account must be marked as primary"),

    // Optional additional fields
    tcAcceptanceIp: z.string().ip("Invalid IP address format").optional(),

    // New compliance fields
    tcAttestation: z.number().int().min(0).max(1).optional(),
    visaDisclosure: z.number().int().min(0).max(1).optional(),
    disclosureIP: z.string().ip("Invalid IP address format").optional(),
    disclosureDate: z
      .string()
      .regex(/^\d{8}$/, "Date must be YYYYMMDD format")
      .optional(),
    merchantIp: z.string().ip("Invalid IP address format").optional(),

    // User account creation fields
    username: z.string().optional(),
    password: z.string().optional(),
    createAccount: z.boolean().optional(),
  })
  .refine(
    (data) => {
      // For sole proprietorship (type 1), single owner must have 100% ownership
      if (data.type === 1) {
        const members = data.merchant.members;
        if (members.length !== 1) {
          return false;
        }
        return members[0].ownership === 10000;
      }
      return true;
    },
    {
      message: "Sole proprietorship must have exactly one owner with 100% ownership (10000 basis points)",
      path: ["merchant", "members"],
    }
  )
  .refine(
    (data) => {
      // For government (type 5) or non-profit (type 6), no ownership structure
      if (data.type === 5 || data.type === 6) {
        return data.merchant.members.length === 0 || data.merchant.members.every((member) => !member.ownership || member.ownership === 0);
      }
      return true;
    },
    {
      message: "Government and non-profit entities should not have ownership structure",
      path: ["merchant", "members"],
    }
  )
  .transform((data) => {
    // Business rule: When status is "1", always set annualCCSales to 0
    if (data.merchant.status === "1") {
      data.merchant.annualCCSales = 0;
    }

    // Ensure public is always 0 for merchant entities (private companies)
    data.public = 0;

    return data;
  })
  .superRefine((data, ctx) => {
    // Only validate username and password if createAccount is true
    if (data.createAccount) {
      // Validate username
      if (!data.username) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Username is required when creating an account",
          path: ["username"],
        });
      } else {
        if (data.username.length < 3) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Username must be at least 3 characters",
            path: ["username"],
          });
        }
        if (data.username.length > 50) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Username must be at most 50 characters",
            path: ["username"],
          });
        }
        if (!/^[a-z0-9]+$/.test(data.username)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Username must contain only lowercase letters and numbers (no uppercase, special characters, or underscores)",
            path: ["username"],
          });
        } else if (!/(?=.*\d)/.test(data.username)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Username must include at least one number",
            path: ["username"],
          });
        }
      }

      // Validate password
      if (!data.password) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Password is required when creating an account",
          path: ["password"],
        });
      } else {
        if (data.password.length < 8) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Password must be at least 8 characters",
            path: ["password"],
          });
        }
        if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/.test(data.password)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Password must contain uppercase, lowercase, number, and special character",
            path: ["password"],
          });
        }
      }
    }
  });

// Export the inferred type
export type OnboardingRequest = z.infer<typeof OnboardingRequestSchema>;

// Validation function with detailed error messages
export function validateOnboardingRequest(data: unknown): {
  success: boolean;
  data?: OnboardingRequest;
  errors?: string[];
} {
  try {
    const validatedData = OnboardingRequestSchema.parse(data);
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map((err) => {
        const path = err.path.length > 0 ? `${err.path.join(".")}: ` : "";
        return `${path}${err.message}`;
      });

      return {
        success: false,
        errors,
      };
    }

    return {
      success: false,
      errors: ["Unknown validation error"],
    };
  }
}
